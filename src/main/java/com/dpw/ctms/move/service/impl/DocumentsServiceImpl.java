package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.config.ConfigService;
import com.dpw.ctms.move.constants.ConfigConstants;
import com.dpw.ctms.move.constants.DocumentFieldConstants;
import com.dpw.ctms.move.specification.DocumentSpecifications;
import com.dpw.ctms.move.dto.document.DeliveryTaskDocumentDTO;
import com.dpw.ctms.move.dto.document.DocumentDTO;
import com.dpw.ctms.move.entity.Document;
import com.dpw.ctms.move.enums.*;
import com.dpw.ctms.move.mapper.DocumentMapper;
import com.dpw.ctms.move.repository.DocumentRepository;
import com.dpw.ctms.move.request.EntityDocumentRequest;
import com.dpw.ctms.move.request.documentEvent.PreSignedUrlEvent;
import com.dpw.ctms.move.response.PreSignedUrlResponse;
import com.dpw.ctms.move.response.DocumentDownloadResponse;
import com.dpw.ctms.move.response.DocumentErrorResponse;
import com.dpw.ctms.move.response.EntityDocumentResponse;
import com.dpw.ctms.move.response.FileDownloadPreSignedUrlResponse;
import com.dpw.ctms.move.service.IDocumentsService;
import com.dpw.tmsutils.exception.TMSException;
import org.springframework.beans.factory.annotation.Value;
import com.dpw.ctms.move.service.document.DocumentGenerator;
import com.dpw.ctms.move.service.document.DocumentGeneratorFactory;
import com.dpw.ctms.move.util.CanonicalChecksum;
import com.dpw.ctms.move.util.DocumentUtil;
import com.dpw.ctms.move.util.JsonUtils;
import com.dpw.tmsutils.schemaobjects.*;
import com.dpw.tmsutils.service.DocumentService;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.data.jpa.domain.Specification;

import java.util.*;

import static com.dpw.ctms.move.constants.ErrorMessageConstant.UPLOAD_FAILED_DOCUMENT_MESSAGE;
import static com.dpw.ctms.move.constants.PropertyConstants.*;
import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.EXTERNAL_INVOCATION_EXCEPTION;

@Service
@RequiredArgsConstructor
@Slf4j
public class DocumentsServiceImpl implements IDocumentsService {

    private final DocumentGeneratorFactory documentGeneratorFactory;
    private final DocumentRepository documentRepository;
    private final CanonicalChecksum canonicalChecksum;
    private final DocumentService documentService;
    private final ConfigService configService;
    private final DocumentMapper documentMapper;
    
    @Value("${documentService.write-count:100}")
    private Integer writeCount;

    @Value("${documentService.subsName:ctms_local-move}")
    private String subsName;


    public DocumentDownloadResponse downloadTripBolDocument(
            String tripCode,
            Tenant tenant
    ) {
        log.info("Generating BOL document for tripCode: {}", tripCode);
        DocumentGenerator<?> generator = documentGeneratorFactory.getGenerator(DocumentType.BOL);
        Object jsonObject = generator.generateJson(tripCode, tenant);
        String checksumString = canonicalChecksum.generateChecksum(jsonObject);

        // Build specification for finding BOL document
        Specification<Document> spec = DocumentSpecifications.bolDocumentSpec(
                tripCode, EntityType.TRIP.name(), DocumentStatus.ACTIVE, 
                DocumentOperationType.DOWNLOAD, checksumString, DocumentType.BOL);

        log.info("Finding BOL document with specification: {}", checksumString);
        Optional<Document> document = documentRepository.findOne(spec);

        if (document.isEmpty()) {
            DocumentDTO documentDTO = DocumentDTO.builder()
                    .entityId(tripCode)
                    .entityType(EntityType.TRIP.name())
                    .documentType(DocumentType.BOL)
                    .status(DocumentStatus.ACTIVE)
                    .documentOperationType(DocumentOperationType.DOWNLOAD)
                    .checksum(checksumString)
                    .build();
            return generateAndSaveDocument(documentDTO, tenant, JsonUtils.toJson(jsonObject));
        } else {
            // Always get fresh presigned URL from document service using fileIdentifier
            GetDownloadPreSignedURLRequest downloadPreSignedURLRequest =
                    GetDownloadPreSignedURLRequest
                            .builder()
                            .fileIdentifiers(Optional.ofNullable(document.get().getFileIdentifier())
                                .map(fileId -> Collections.singletonList(UUID.fromString(fileId)))
                                .orElse(Collections.emptyList()))
                            .readExpiryDurationInMinutes(LINK_EXPIRY_DURATION)
                            .build();

            DocumentServiceResponse<List<DownloadPreSignedURLResponse>> documentServiceResponse = documentService.getDownloadPreSignedURLWithToken(downloadPreSignedURLRequest);
            String presignedUrl = documentServiceResponse.getData().getFirst().getPreSignedUrl();
            return DocumentDownloadResponse.builder()
                    .presignedDownloadUrl(presignedUrl)
                    .fileIdentifier(document.get().getFileIdentifier())
                    .build();
        }
    }

    private DocumentDownloadResponse generateAndSaveDocument(DocumentDTO documentDTO, Tenant tenant, String json) {
        log.info("Generating and saving document for entityId: {}, documentType: {}", documentDTO.getEntityId(), documentDTO.getDocumentType());

        JsonNode config = configService.getConfig(ConfigConstants.BOL_CONFIG, tenant);
        String templateId = config.get("templateId").asText();
        log.info("Template id: {}", templateId);
        
        if (templateId == null) {
            throw new IllegalArgumentException("Template ID not found in config for documentType: " + 
                    documentDTO.getDocumentType() + ", vendor: " + tenant);
        }

        PrintBolRequest<JsonNode> printBolRequest = PrintBolRequest.<JsonNode>builder()
                .data(JsonUtils.toJsonNodeFromString(json))
                .bolConfiguration(PrintBolRequest.BolConfiguration.builder()
                        .type(FILE_TYPE_PDF).responseType(PRESIGNED_DOWNLOAD_LINK).linkExpiryDuration(LINK_EXPIRY_DURATION)
                        .build())
                .build();

        DocumentServiceResponse<PrintBolResponse> response = documentService.getBol(printBolRequest, templateId);
        log.info("Response from DocumentService: {}", response);

        if (response.isError()) {
            log.error("Unable to Generate Downlaod Url error: {}", response);
            throw new TMSException(EXTERNAL_INVOCATION_EXCEPTION.name(), response.getErrorDescription());
        }

        Document document = documentMapper.toEntity(documentDTO);
        document.setFileIdentifier(response.getData().getFileIdentifier());

        documentRepository.save(document);
        log.info("Saved document to database for entityId: {}, entityType: {}, documentType: {}", 
                documentDTO.getEntityId(), documentDTO.getEntityType(), documentDTO.getDocumentType());
        
        return DocumentDownloadResponse.builder()
                .presignedDownloadUrl(response.getData().getPresignedDownloadUrl())
                .fileIdentifier(response.getData().getFileIdentifier())
                .build();
    }

    @Override
    public PreSignedUrlResponse getPreSignedUrl() {
        GetPreSignedURLRequest getPreSignedURLRequest = GetPreSignedURLRequest.builder()
                .clientIdentifier(UUID.randomUUID().toString())
                .writeCount(writeCount)
                .writeExpiryDurationInMinutes(LINK_EXPIRY_DURATION)
                .subscriptionName(subsName)
                .build();
        DocumentServiceResponse<GetPreSignedURLResponse> preSignedUrlResponse = documentService.getPreSignedURL(getPreSignedURLRequest);
        if (preSignedUrlResponse.isError()) {
            log.error("GetPreSignedURLResponse error: {}", preSignedUrlResponse);
            throw new TMSException(EXTERNAL_INVOCATION_EXCEPTION.name(), preSignedUrlResponse.getErrorDescription());
        }
        return PreSignedUrlResponse.builder()
                .preSignedURL(preSignedUrlResponse.getData().getPreSignedUrl())
                .clientIdentifier(getPreSignedURLRequest.getClientIdentifier()).build();
    }

    @Override
    public void findAndUpdate(PreSignedUrlEvent preSignedUrlEvent) {
        log.info("Processing pre-signed url event for fileKey: {}", preSignedUrlEvent.getFileKey());
        Document document = processDocument(preSignedUrlEvent);
        if (document == null) {
            return;
        }
        document = documentRepository.save(document);
        log.info("Document db data saved with document: {}", document);
    }

    @Override
    public void findAndUpdate(DeliveryTaskDocumentDTO deliveryTaskDocumentDTO) {
        log.info("Processing delivery task event for fileIdentifier: {}, entityId: {}, entityType: {}, operationType: {}",
                deliveryTaskDocumentDTO.getAsyncMappingUUID(), deliveryTaskDocumentDTO.getEntityId(),
                deliveryTaskDocumentDTO.getEntityType(), deliveryTaskDocumentDTO.getOperationType());

        Document document = processDocument(deliveryTaskDocumentDTO);
        if (document == null) {
            return;
        }
        List<Document> activeEntityDocuments = getActiveEntityDocuments(deliveryTaskDocumentDTO);
        log.info("Active entity documents: {}", activeEntityDocuments);
        discardDocuments(activeEntityDocuments.stream().filter(doc -> doc.getAsyncMappingUUID() != null
                            && !doc.getAsyncMappingUUID().equals(deliveryTaskDocumentDTO.getAsyncMappingUUID())).toList());
        
        documentRepository.save(document);
        log.info("Delivery task event processed successfully for asynMappingUUID: {}", deliveryTaskDocumentDTO.getAsyncMappingUUID());
    }


    private List<Document> getActiveEntityDocuments(DeliveryTaskDocumentDTO deliveryTaskDocumentDTO) {
        Specification<Document> spec = DocumentSpecifications.activeEntityDocumentsSpec(
                deliveryTaskDocumentDTO.getEntityId(), 
                deliveryTaskDocumentDTO.getEntityType(),
                deliveryTaskDocumentDTO.getOperationType(), 
                DocumentStatus.ACTIVE);
                
        return documentRepository.findAll(spec);
    }


    private void discardDocuments(List<Document> documentsToDiscard) {
        if (documentsToDiscard != null && !documentsToDiscard.isEmpty()) {
            documentsToDiscard.forEach(doc -> {
                doc.setStatus(DocumentStatus.DISCARDED);
                log.info("Marking document {} as discarded", doc.getAsyncMappingUUID());
            });
            documentRepository.saveAll(documentsToDiscard);
        }
    }

    private <T> Document processDocument(T eventData) {
        String asyncMappingUUID = extractAsyncMappingUUID(eventData);
        Specification<Document> spec = DocumentSpecifications.byAsyncMappingUUIDSpec(asyncMappingUUID);
        Optional<Document> documentOptional = documentRepository.findOne(spec);
        DocumentStatus status = documentOptional.map(Document::getStatus).orElse(null);
        
        switch (status) {
            case ACTIVE:
                log.info("Document with asyncMappingUUID: {} is already active. No action needed.", asyncMappingUUID);
                return null;
                
            case DISCARDED:
                log.error("Document is Discarded for asyncMappingUUID: {}. Cannot update the status!", asyncMappingUUID);
                return null;
                
            case INACTIVE:
                if (documentOptional.isPresent()) {
                    log.info("Updating existing inactive document with asyncMappingUUID: {}", asyncMappingUUID);
                    updateDocumentByType(documentOptional.get(), eventData);
                    documentOptional.get().setStatus(DocumentStatus.ACTIVE);
                    return documentOptional.get();
                } else {
                    log.warn("Document optional is empty despite INACTIVE status for asyncMappingUUID: {}", asyncMappingUUID);
                    return null;
                }
                
            case null:
            default:
                log.info("Creating new document for asyncMappingUUID: {}", asyncMappingUUID);
                return createDocumentByType(eventData);
        }
    }
    
    private <T> String extractAsyncMappingUUID(T eventData) {
        if (eventData instanceof PreSignedUrlEvent) {
            return ((PreSignedUrlEvent) eventData).getFileKey();
        } else if (eventData instanceof DeliveryTaskDocumentDTO) {
            return ((DeliveryTaskDocumentDTO) eventData).getAsyncMappingUUID();
        }
        throw new IllegalArgumentException("Unsupported event type: " + eventData.getClass());
    }
    
    private <T> void updateDocumentByType(Document document, T eventData) {
        if (eventData instanceof PreSignedUrlEvent) {
            documentMapper.updateDocument(document, (PreSignedUrlEvent) eventData);
        } else if (eventData instanceof DeliveryTaskDocumentDTO) {
            documentMapper.updateDocument(document, (DeliveryTaskDocumentDTO) eventData);
        }
    }
    
    private <T> Document createDocumentByType(T eventData) {
        if (eventData instanceof PreSignedUrlEvent) {
             return documentMapper.createDocument((PreSignedUrlEvent) eventData);
        } else if (eventData instanceof DeliveryTaskDocumentDTO) {
            return documentMapper.createDocument((DeliveryTaskDocumentDTO) eventData);
        }
        throw new IllegalArgumentException("Unsupported event type: " + eventData.getClass());
    }

    @Override
    public DocumentErrorResponse getAllErrors(List<String> asyncMappingUUIDs) {
        log.info("Checking document status for asyncMappingUUIDs: {}", asyncMappingUUIDs);
        
        Specification<Document> spec = DocumentSpecifications.byAsyncMappingUUIDsInSpec(asyncMappingUUIDs);
        List<Document> documents = documentRepository.findAll(spec);
        DocumentErrorResponse response = new DocumentErrorResponse();
        List<DocumentErrorResponse.FileErrorDetails> errorDetails = new ArrayList<>();
        
        // Get async mapping UUIDs of found documents
        Set<String> foundAsyncMappingUUIDs = documents.stream()
                .map(Document::getAsyncMappingUUID)
                .collect(java.util.stream.Collectors.toSet());

        if (!foundAsyncMappingUUIDs.isEmpty()) {
            // Check for missing async mapping UUIDs
            for (String asyncMappingUUID : asyncMappingUUIDs) {
                if (!foundAsyncMappingUUIDs.contains(asyncMappingUUID)) {
                    errorDetails.add(DocumentErrorResponse.FileErrorDetails.builder()
                            .error(UPLOAD_FAILED_DOCUMENT_MESSAGE)
                            .fileIdentifier(asyncMappingUUID)
                            .build());
                    log.info("Document with asyncMappingUUID: {} not found", asyncMappingUUID);
                }
            }

            // Check for inactive documents
            for (Document document : documents) {
                if (DocumentUtil.isDocumentInactive(document)) {
                    errorDetails.add(DocumentErrorResponse.FileErrorDetails.builder()
                            .error(UPLOAD_FAILED_DOCUMENT_MESSAGE)
                            .asyncMappingUUID(document.getAsyncMappingUUID())
                            .fileName(document.getFileName())
                            .fileSize(document.getFileSize())
                            .fileType(document.getFileType())
                            .fileIdentifier(document.getFileIdentifier())
                            .build());
                    log.info("Document with asyncMappingUUID: {} is inactive", document.getAsyncMappingUUID());
                }
            }
        }
        
        if (errorDetails.isEmpty()) {
            response.setFailedDocumentDetails(null);
            log.info("All documents are active for asyncMappingUUIDs: {}", asyncMappingUUIDs);
        } else {
            response.setFailedDocumentDetails(errorDetails);
            log.info("Found {} failed documents out of {} total asyncMappingUUIDs", errorDetails.size(), asyncMappingUUIDs.size());
        }
        
        return response;
    }

    @Override
    public EntityDocumentResponse getDocumentsByEntity(List<EntityDocumentRequest> entityRequests) {
        log.info("Getting documents for {} entity requests", entityRequests.size());
        
        List<EntityDocumentResponse.FileDetail> allFileDetails = new ArrayList<>();
        
        for (EntityDocumentRequest request : entityRequests) {

            Specification<Document> spec = DocumentSpecifications.activeEntityDocumentsSpec(
                    request.getEntityCode(), 
                    request.getEntityType(),
                    DocumentOperationType.UPLOAD,
                    DocumentStatus.ACTIVE
            );
            
            List<Document> documents = documentRepository.findAll(spec);

            for (Document document : documents) {
                allFileDetails.add(documentMapper.toFileDetail(document));
            }
        }

        EntityDocumentResponse.ResponseData responseData = EntityDocumentResponse.ResponseData.builder()
                .fileDetails(allFileDetails)
                .build();
                
        return EntityDocumentResponse.builder()
                .data(responseData)
                .build();
    }

    @Override
    public FileDownloadPreSignedUrlResponse getFileDownloadUrls(List<String> externalDocumentIdentifiers) {
        log.info("Getting download URLs for {} external document identifiers", externalDocumentIdentifiers.size());
        
        try {
            List<UUID> fileIdentifierUUIDs = new ArrayList<>();
            
            for (String identifier : externalDocumentIdentifiers) {
                try {
                    UUID uuid = UUID.fromString(identifier);
                    fileIdentifierUUIDs.add(uuid);
                } catch (IllegalArgumentException e) {
                    log.error("Invalid UUID format for identifier: {}", identifier);
                    throw new TMSException(EXTERNAL_INVOCATION_EXCEPTION.name(),"Invalid file identifier format: " + identifier);
                }
            }

            GetDownloadPreSignedURLRequest downloadPreSignedURLRequest = GetDownloadPreSignedURLRequest
                    .builder()
                    .fileIdentifiers(fileIdentifierUUIDs)
                    .readExpiryDurationInMinutes(LINK_EXPIRY_DURATION)
                    .build();

            DocumentServiceResponse<List<DownloadPreSignedURLResponse>> response = 
                    documentService.getDownloadPreSignedURLWithToken(downloadPreSignedURLRequest);
            
            if (response.isError()) {
                log.error("Error getting presigned URLs from document service: {}", response.getErrorDescription());
                throw new TMSException(EXTERNAL_INVOCATION_EXCEPTION.name(), response.getErrorDescription());
            }

            Map<String, String> urlMap = new HashMap<>();
            for (DownloadPreSignedURLResponse urlResponse : response.getData()) {
                urlMap.put(urlResponse.getFileIdentifier(), urlResponse.getPreSignedUrl());
            }
            
            return FileDownloadPreSignedUrlResponse.builder()
                    .data(urlMap)
                    .build();
                    
        } catch (TMSException e) {
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error getting presigned URLs", e);
            throw new TMSException(EXTERNAL_INVOCATION_EXCEPTION.name() ,"Failed to get presigned download URLs");
        }
    }

}
